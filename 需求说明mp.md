
## 需求说明

### 核心功能与流程

本项目将开发一个**Edge浏览器插件**和一个**云服务器端程序**。

#### 1. 插件端功能 (Edge 浏览器插件)

* **临时邮箱生成：** 在本地浏览器中，生成一个以用户指定域名（`shengchai.dpdns.org`）为后缀的随机字符串邮箱地址。随机字符串长度范围为 8 到 12 位。
* **信息传输：** 将生成的临时邮箱地址及其生成时间，通过安全通道（API）发送至后端云服务器。
* **邮件数据显示：** 接收来自云服务器的邮件数据，并清晰地展示给用户。
* **验证码/链接提取：** 根据用户自定义或共享的解析规则，自动从邮件内容中提取验证码或链接。
    * **修正点：** 如果没有符合的解析规则，插件将**完整展示邮件原文**。用于显示解析出的链接和验证码的区域将**留空**。
* **解析规则管理：**
    * **规则创建：** 用户可输入目标平台的发件人邮箱（例如 `<EMAIL>`）、提供一封包含验证码/链接的完整邮件示例，并手动提取出验证码示例（例如 `986558`），用于生成解析模式。
    * **规则存储与共享：** 用户可选择将解析规则保存到本地浏览器（仅供当前用户使用）或上传至云服务器（共享给所有用户）。
    * **规则应用：** 插件将优先使用本地解析规则，若无匹配则尝试使用云服务器上的共享规则，根据收到的邮件地址自动进行解析。

---

### 事件发生与数据流转详细说明

#### 1. 临时邮箱生成与邮件获取流程

1.  **事件：用户打开 Edge 浏览器插件。**
    * **插件：**
        * 在用户界面上显示生成临时邮箱的选项。
        * 用户点击“生成”按钮。
        * **本地生成**一个随机字符串（8-12位），并拼接上用户配置的域名 (`shengchai.dpdns.org`)，例如 `<EMAIL>`。
        * 记录该临时邮箱的**生成时间**。
        * 将**临时邮箱地址**和**生成时间**通过 HTTPS POST 请求发送到云服务器的 API 接口（例如 `https://1.12.224.176/api/generate-email`）。此请求中可能包含一个用户ID（时间戳+随机数组合），用于后续的数据隔离。

2.  **事件：云服务器接收到临时邮箱生成请求。**
    * **云服务器 (后端程序)：**
        * 接收到来自插件的临时邮箱地址、生成时间和用户ID。
        * **临时存储**这些信息（例如在内存中，与用户ID关联）。
        * **启动邮件收取流程：**
            * 使用 POP3 协议连接 `pop.126.com:995`。
            * 使用中转邮箱 `<EMAIL>` 和授权码 `TQVTv7gUbCGy9eBR` 进行认证。
            * **核心逻辑：** 遍历中转邮箱中的邮件，**只获取邮件发送时间晚于**插件发送过来的“临时邮箱生成时间”的邮件。
            * 对获取到的每封邮件，检查其收件人是否包含当前用户生成的临时邮箱地址。
            * 将符合条件的邮件内容（包括发件人、主题、邮件正文等）打包成数据结构。
            * 通过 HTTPS GET 请求将邮件数据返回给请求的插件（例如 `https://1.12.224.176/api/get-emails?userId=xxx`）。

3.  **事件：插件接收到云服务器返回的邮件数据。**
    * **插件：**
        * 接收到云服务器返回的邮件数据。
        * 在插件界面上**显示**这些邮件。
        * **触发解析逻辑：**
            * 根据每封邮件的**发件人邮箱地址**，在**本地存储的解析规则**中查找匹配项。
            * 如果本地没有匹配项，则向云服务器请求**共享解析规则**（例如 `https://1.12.224.176/api/get-shared-rules`），并在共享规则中查找匹配项。
            * 如果找到匹配的解析规则，则根据该规则从邮件正文中**自动提取**验证码或链接。将提取到的验证码或链接在插件界面上高亮显示或单独展示。
            * **修正点：** 如果**没有**找到匹配的解析规则，插件将**完整展示邮件原文**，而**解析出的链接和验证码区域则保持空着**。

